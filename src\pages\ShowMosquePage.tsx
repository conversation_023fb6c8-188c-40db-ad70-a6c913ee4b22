import { useState, useEffect, useCallback, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  getFirestore,
  collection,
  query,
  where,
  getDocs,
  onSnapshot,
} from "firebase/firestore";
import { Mosque } from "../models/Mosque";
import { PrayerTime } from "../models/PrayerTime";
import Tooltip from "../components/Tooltip";
import HijriDate from "hijri-date";

// Add these utility functions at the top
const convertToHindiDigits = (num: number): string => {
  const hindiDigits = ["۰", "۱", "۲", "۳", "٤", "۵", "٦", "۷", "۸", "۹"];
  return num.toString().replace(/\d/g, (d) => hindiDigits[+d]);
};

const getHijriDate = () => {
  const hijri = new HijriDate();

  const hijriMonths: Record<number, string> = {
    1: "محرم",
    2: "صفر",
    3: "ربيع الأول",
    4: "ربيع الثاني",
    5: "جمادى الأولى",
    6: "جمادى الآخرة",
    7: "رجب",
    8: "شعبان",
    9: "رمضان",
    10: "شوال",
    11: "ذو القعدة",
    12: "ذو الحجة",
  };

  const adjustedDate = hijri.date - 1;

  if (adjustedDate === 0) {
    console.warn("Date adjustment needed for month rollover");
    return `${convertToHindiDigits(hijri.date)} ${
      hijriMonths[hijri.month as keyof typeof hijriMonths]
    } (${convertToHindiDigits(hijri.month)}) ${convertToHindiDigits(
      hijri.year
    )} هـ`;
  }

  return `${convertToHindiDigits(adjustedDate)} ${
    hijriMonths[hijri.month as keyof typeof hijriMonths]
  } (${convertToHindiDigits(hijri.month)}) ${convertToHindiDigits(
    hijri.year
  )} هـ`;
};

const getGregorianDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const adjustTime = (
  timeStr: string,
  minutes: number,
  isDST: boolean = false
): string => {
  const [hours, mins] = timeStr.split(":").map(Number);
  const date = new Date();

  // Convert PM times to 24-hour format
  let adjustedHours = hours;
  if (timeStr.includes("PM")) {
    adjustedHours = hours === 12 ? 12 : hours + 12;
  }

  // Add an extra hour if daylight saving is active
  const dstAdjustment = isDST ? 1 : 0;
  date.setHours(adjustedHours + dstAdjustment, mins + minutes);

  // Use 24-hour format (HH:mm)
  return date.toLocaleTimeString("he-IL", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

const timeToMinutes = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 60 + minutes;
};

const getCurrentTimeInMinutes = (): number => {
  const now = new Date();
  const totalMinutes = now.getHours() * 60 + now.getMinutes();
  const totalSeconds = totalMinutes * 60 + now.getSeconds();
  return totalSeconds;
};

const getPrayerStatus = (
  prayerTime: string,
  iqamaShift: number,
  adhanCountDown: number,
  prayerName: PrayerName,
  allPrayerTimes: Record<PrayerName, string>
): "approaching" | "prayer-time" | "iqama-time" | "normal" => {
  const currentMinutes = getCurrentTimeInMinutes() / 60; // Convert seconds to minutes
  const prayerMinutes = timeToMinutes(prayerTime);
  const iqamaMinutes = prayerMinutes + iqamaShift;

  // First check if we're approaching this prayer
  if (
    currentMinutes >= prayerMinutes - adhanCountDown &&
    currentMinutes < prayerMinutes
  ) {
    return "approaching";
  }

  // Convert all prayer times to minutes for comparison
  const times = {
    fajr: timeToMinutes(allPrayerTimes.fajr),
    shorouq: timeToMinutes(allPrayerTimes.shorouq),
    duhr: timeToMinutes(allPrayerTimes.duhr),
    asr: timeToMinutes(allPrayerTimes.asr),
    maghreb: timeToMinutes(allPrayerTimes.maghreb),
    eshaa: timeToMinutes(allPrayerTimes.eshaa),
  };

  // Function to check if this is the current active prayer
  const isCurrentPrayer = () => {
    const prayerOrder = ["fajr", "shorouq", "duhr", "asr", "maghreb", "eshaa"];
    const currentPrayerIndex = prayerOrder.indexOf(prayerName.toLowerCase());
    const nextPrayerName =
      prayerOrder[(currentPrayerIndex + 1) % prayerOrder.length];
    const nextPrayerTime = times[nextPrayerName as keyof typeof times];

    // Special handling for Isha prayer (active from Isha time until midnight)
    if (prayerName.toLowerCase() === "eshaa") {
      return currentMinutes >= times.eshaa && currentMinutes < 24 * 60; // Before midnight
    }

    // For all other prayers
    return currentMinutes >= prayerMinutes && currentMinutes < nextPrayerTime;
  };

  // Only proceed with active prayer status checks if this is the current prayer
  if (!isCurrentPrayer()) {
    return "normal";
  }

  if (currentMinutes >= prayerMinutes && currentMinutes < iqamaMinutes) {
    return "prayer-time";
  } else if (currentMinutes >= iqamaMinutes) {
    return "iqama-time";
  }

  return "normal";
};

const convertTo24Hour = (timeStr: string, prayerName?: string): string => {
  const [hoursStr, minutesStr] = timeStr.split(":");
  let hours = Number(hoursStr);
  const minutes = Number(minutesStr);

  // Only convert afternoon/evening prayers to 24-hour format
  if (
    prayerName === "asr" ||
    prayerName === "maghreb" ||
    prayerName === "eshaa"
  ) {
    hours += 12;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
};

type PrayerName = "fajr" | "shorouq" | "duhr" | "asr" | "maghreb" | "eshaa";

export const getPrayerArabicName = (prayer: PrayerName): string => {
  const nameMap: Record<PrayerName, string> = {
    fajr: "الفجر",
    shorouq: "الشروق",
    duhr: "الظهر",
    asr: "العصر",
    maghreb: "المغرب",
    eshaa: "العشاء",
  };
  return nameMap[prayer];
};

const formatTimeRemaining = (totalSeconds: number): string => {
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

const CountdownDisplay = ({
  prayerTimes,
  mosque,
}: {
  prayerTimes: PrayerTime | null;
  mosque: Mosque | null;
}) => {
  const [countdown, setCountdown] = useState<string>("");
  const [countdownType, setCountdownType] = useState<
    "adhan" | "iqama" | "none"
  >("none");

  useEffect(() => {
    const calculateCountdown = () => {
      if (!prayerTimes || !mosque) return;

      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      // const currentSeconds = currentMinutes * 60 + now.getSeconds();
      const prayerOrder: PrayerName[] = [
        "fajr",
        "shorouq",
        "duhr",
        "asr",
        "maghreb",
        "eshaa",
      ];

      for (const prayer of prayerOrder) {
        const prayerTime = timeToMinutes(prayerTimes[prayer]);
        const prayerTimeInSeconds = prayerTime * 60;
        const iqamaTime =
          prayerTime +
          ((mosque[
            `iqama${
              prayer.charAt(0).toUpperCase() + prayer.slice(1)
            }Shift` as keyof Mosque
          ] as number) || 0);
        const iqamaTimeInSeconds = iqamaTime * 60;

        // Check for Adhan countdown
        if (
          currentMinutes >= prayerTime - (mosque.adhanCountDown || 30) &&
          currentMinutes < prayerTime
        ) {
          const remainingSeconds =
            prayerTimeInSeconds - (currentMinutes * 60 + now.getSeconds());
          if (prayer === "shorouq") {
            setCountdown(
              `باقي للشروق ${formatTimeRemaining(remainingSeconds)} د'`
            );
            console.log(remainingSeconds);
          } else {
            setCountdown(
              `باقي للأذان ${formatTimeRemaining(remainingSeconds)} د'`
            );
          }
          setCountdownType("adhan");
          return;
        }

        // Check for Iqama countdown
        if (currentMinutes >= prayerTime && currentMinutes < iqamaTime) {
          const remainingSeconds =
            iqamaTimeInSeconds - (currentMinutes * 60 + now.getSeconds());
          setCountdown(
            `باقي للإقامة ${formatTimeRemaining(remainingSeconds)} د'`
          );
          setCountdownType("iqama");
          return;
        }
      }

      setCountdown("");
      setCountdownType("none");
    };

    const timer = setInterval(calculateCountdown, 1000);
    return () => clearInterval(timer);
  }, [prayerTimes, mosque]);

  if (countdownType === "none") {
    return mosque?.dedication ? (
      <div className="text-center text-gray-600 dark:text-gray-300 mt-4 text-2xl font-bold leading-normal">
        {mosque.dedication}
      </div>
    ) : null;
  }

  return (
    <div
      className={`text-center mt-4 text-2xl font-bold ${
        countdownType === "adhan"
          ? "text-yellow-600 dark:text-yellow-400"
          : "text-blue-600 dark:text-blue-400"
      }`}
    >
      {countdown}
    </div>
  );
};

export default function ShowMosquePage() {
  const { friendlyUrlName } = useParams();
  const navigate = useNavigate();
  const [mosque, setMosque] = useState<Mosque | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [prayerTimes, setPrayerTimes] = useState<PrayerTime | null>(null);
  const [lastFetchDate, setLastFetchDate] = useState(new Date());
  const [scrollDuration, setScrollDuration] = useState(30);
  const scrollTextRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    if (newDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };

  // Add effect to check dark mode
  useEffect(() => {
    setIsDarkMode(document.documentElement.classList.contains("dark"));
  }, []);

  // Handle fullscreen
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setShowMenu(false); // Close menu after toggling fullscreen
  };

  // Handle page reload
  const handleReload = () => {
    window.location.reload();
    setShowMenu(false); // Close menu after reload
  };

  // Clock update effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Slide rotation effect
  useEffect(() => {
    if (!mosque?.sliderItems?.length) return;

    const slideInterval = setInterval(() => {
      setCurrentSlideIndex((current) =>
        current === mosque.sliderItems.length - 1 ? 0 : current + 1
      );
    }, (mosque.sliderDelayTime || 5) * 1000);

    return () => clearInterval(slideInterval);
  }, [mosque?.sliderItems, mosque?.sliderDelayTime]);

  useEffect(() => {
    console.log("Starting to listen to mosque data...");
    setLoading(true);

    const db = getFirestore();
    const mosquesRef = collection(db, "mosques");
    const q = query(
      mosquesRef,
      where("friendlyUrlName", "==", friendlyUrlName)
    );

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        if (querySnapshot.empty) {
          console.log("No mosque found with this friendlyUrlName");
          setError("Mosque not found");
          setLoading(false);
          return;
        }

        const mosqueData = querySnapshot.docs[0].data() as Mosque;
        console.log("Mosque data updated:", mosqueData);
        setMosque(mosqueData);
        document.title = `${mosqueData.name} - ${mosqueData.city}`;
        setLoading(false);
      },
      (error) => {
        console.error("Error listening to mosque data:", error);
        setError("Failed to load mosque data");
        setLoading(false);
      }
    );

    // Cleanup listener on component unmount
    return () => {
      unsubscribe();
      document.title = "المساجد";
    };
  }, [friendlyUrlName]);

  const fetchPrayerTimes = useCallback(async () => {
    if (!mosque) {
      console.log("No mosque data available");
      return;
    }

    try {
      const today = new Date();
      const dayOfMonth = today.getDate().toString();
      const monthNumber = (today.getMonth() + 1).toString();

      console.log("Fetching prayer times with params:", {
        dayOfMonth,
        monthNumber,
        timeZone: mosque.timeZone,
        isDST: mosque.dayLightSaving,
      });

      const db = getFirestore();
      const prayerTimesRef = collection(db, "prayer-times");
      const q = query(
        prayerTimesRef,
        where("dayOfMonth", "==", dayOfMonth),
        where("monthNumber", "==", monthNumber),
        where("timeZone", "==", mosque.timeZone)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const prayerTimeData = querySnapshot.docs[0].data() as PrayerTime;
        const dahriShift = Number(mosque.dahriShift) || 0;
        const isDST = mosque.dayLightSaving || false;

        const adjustedTimes = {
          ...prayerTimeData,
          fajr: adjustTime(
            convertTo24Hour(prayerTimeData.fajr),
            dahriShift,
            isDST
          ),
          shorouq: adjustTime(
            convertTo24Hour(prayerTimeData.shorouq),
            dahriShift,
            isDST
          ),
          duhr: adjustTime(
            convertTo24Hour(prayerTimeData.duhr),
            dahriShift,
            isDST
          ),
          asr: adjustTime(
            convertTo24Hour(prayerTimeData.asr, "asr"),
            dahriShift,
            isDST
          ),
          maghreb: adjustTime(
            convertTo24Hour(prayerTimeData.maghreb, "maghreb"),
            dahriShift,
            isDST
          ),
          eshaa: adjustTime(
            convertTo24Hour(prayerTimeData.eshaa, "eshaa"),
            dahriShift,
            isDST
          ),
        };

        console.log("Adjusted prayer times (including DST):", adjustedTimes);
        setPrayerTimes(adjustedTimes);
      } else {
        console.log("No prayer times found for the new day");
      }
    } catch (err) {
      console.error("Error fetching prayer times:", err);
    }
  }, [mosque]);

  useEffect(() => {
    if (mosque && mosque.timeZone) {
      fetchPrayerTimes();
    }
  }, [mosque, fetchPrayerTimes]);

  // Add this function to check if we need to fetch new times
  const shouldFetchNewTimes = (currentDate: Date, lastFetchDate: Date) => {
    return (
      currentDate.getDate() !== lastFetchDate.getDate() ||
      currentDate.getMonth() !== lastFetchDate.getMonth()
    );
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();

      // Check if we need to fetch new prayer times
      if (shouldFetchNewTimes(now, lastFetchDate)) {
        fetchPrayerTimes();
        setLastFetchDate(now);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [lastFetchDate, fetchPrayerTimes]);

  // Calculate scroll duration for constant speed
  useEffect(() => {
    if (scrollTextRef.current && containerRef.current && mosque?.news) {
      const textWidth = scrollTextRef.current.scrollWidth;
      const containerWidth = containerRef.current.offsetWidth;
      const totalDistance = textWidth + containerWidth; // Text width + container width for full scroll

      // Set speed: pixels per second (adjust this value to change speed)
      const pixelsPerSecond = 100; // You can adjust this for faster/slower scrolling
      const duration = totalDistance / pixelsPerSecond;

      setScrollDuration(duration);
    }
  }, [mosque?.news]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-xl text-gray-800 dark:text-white">
          جاري التحميل...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-xl text-red-600 dark:text-red-400">{error}</div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      {/* Top Bar */}
      <div className="h-20 bg-green-600 dark:bg-green-800 shadow-lg">
        <div className="h-full px-6 flex items-center justify-between">
          {/* Right Section - Menu and Dates */}
          <div className="flex items-center gap-6">
            {/* Hamburger Menu */}
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="text-white hover:bg-green-500 p-2 rounded-lg"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-50">
                  <button
                    onClick={() => navigate("/signin")}
                    className="w-full text-right px-4 py-2 text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <svg
                      className="w-5 h-5 text-gray-700 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    تعديل
                  </button>
                  <button
                    onClick={toggleFullScreen}
                    className="w-full text-right px-4 py-2 text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <svg
                      className="w-5 h-5 text-gray-700 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                      />
                    </svg>
                    ملء الشاشة
                  </button>
                  <button
                    onClick={handleReload}
                    className="w-full text-right px-4 py-2 text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <svg
                      className="w-5 h-5 text-gray-700 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    تحديث
                  </button>
                </div>
              )}
            </div>

            {/* Theme Toggle */}
            <Tooltip text={isDarkMode ? "وضع النهار" : "وضع الليل"}>
              <button
                onClick={toggleDarkMode}
                className="text-white hover:bg-green-500 p-2 rounded-lg"
              >
                {isDarkMode ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-yellow-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                    />
                  </svg>
                )}
              </button>
            </Tooltip>

            {/* Dates */}
            <div className="text-white text-2xl flex items-center">
              <span dir="rtl" className="font-bold text-yellow-300">
                {getHijriDate()}
              </span>
              <span className="mr-4 text-gray-200" dir="ltr">
                {getGregorianDate()}
              </span>
            </div>
          </div>

          {/* Center - Mosque Name */}
          {/* <div className="text-4xl font-black text-white text-center flex-1 flex items-center justify-center">
            <span>
              {mosque?.name ? `${mosque.name} - ${mosque.city}` : "المسجد"}
            </span>
          </div> */}

          {/* Left Section - Time */}
          <div className="text-6xl font-black text-yellow-300 digital-clock w-[280px] text-left">
            {currentTime.toLocaleTimeString("he-IL", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
              hour12: false,
            })}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex h-[calc(100vh-12rem)]">
        {/* Right Side - Slides */}
        <div className="w-[70%] p-0 bg-gray-100 dark:bg-gray-800">
          <div className="h-full border-2 border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden relative">
            {mosque?.sliderItems?.map((slide, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-1000
                  ${index === currentSlideIndex ? "opacity-100" : "opacity-0"}`}
              >
                <img
                  src={slide.imageUrl}
                  alt={slide.text}
                  className="w-full h-full object-fill"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Left Side - Prayer Times */}
        <div className="w-[30%] p-0">
          <div className="h-full border-2 border-gray-200 dark:border-gray-700 rounded-lg p-4 py-1 overflow-y-auto">
            {/* Center - Mosque Name */}
            <div className="text-3xl font-black text-white text-center flex-1 flex items-center justify-center my-1">
              <span>
                {mosque?.name ? `${mosque.name} - ${mosque.city}` : "المسجد"}
              </span>
            </div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4 text-center">
              توقيت {mosque?.regionalCity} - ({mosque?.referenceCity}
              {mosque!.dahriShift > 0 ? "+" : ""}
              {mosque?.dahriShift != 0 ? mosque!.dahriShift + "د'" : ""})
            </h2>
            <div className="space-y-4">
              <PrayerTimeRow
                name="الفجر"
                time={prayerTimes?.fajr ?? "--:--"}
                iqamaShift={mosque?.iqamaFajrShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
              <PrayerTimeRow
                name="الشروق"
                time={prayerTimes?.shorouq ?? "--:--"}
                iqamaShift={mosque?.iqamaShorouqShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
              <PrayerTimeRow
                name="الظهر"
                time={prayerTimes?.duhr ?? "--:--"}
                iqamaShift={mosque?.iqamaDuhrShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
              <PrayerTimeRow
                name="العصر"
                time={prayerTimes?.asr ?? "--:--"}
                iqamaShift={mosque?.iqamaAsrShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
              <PrayerTimeRow
                name="المغرب"
                time={prayerTimes?.maghreb ?? "--:--"}
                iqamaShift={mosque?.iqamaMaghrebShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
              <PrayerTimeRow
                name="العشاء"
                time={prayerTimes?.eshaa ?? "--:--"}
                iqamaShift={mosque?.iqamaEshaaShift || 0}
                adhanCountDown={mosque?.adhanCountDown || 30}
                prayerTimes={
                  prayerTimes ?? {
                    fajr: "--:--",
                    shorouq: "--:--",
                    duhr: "--:--",
                    asr: "--:--",
                    maghreb: "--:--",
                    eshaa: "--:--",
                  }
                }
              />
            </div>
            <CountdownDisplay prayerTimes={prayerTimes} mosque={mosque} />
          </div>
        </div>
      </div>

      {/* Footer Bar with Scrolling News */}
      <div className="h-24 bg-green-600 dark:bg-green-800 shadow-lg">
        <div ref={containerRef} className="h-full relative overflow-hidden">
          <div
            ref={scrollTextRef}
            className="absolute whitespace-nowrap"
            style={{
              height: "100%",
              display: "flex",
              alignItems: "center",
              left: "-100%",
              animation: `news-scroll ${scrollDuration}s linear infinite`,
            }}
          >
            <span className="text-3xl text-white font-bold pl-8">
              {mosque?.news || "أهلاً وسهلاً بكم في المسجد"}
            </span>
          </div>
        </div>

        <style>
          {`
            @keyframes news-scroll {
              from { transform: translateX(-100%); }
              to { transform: translateX(100%); }
            }
          `}
        </style>
      </div>
    </div>
  );
}

const PrayerTimeRow = ({
  name,
  time,
  iqamaShift,
  adhanCountDown,
  prayerTimes,
}: {
  name: string;
  time: string;
  iqamaShift: number;
  adhanCountDown: number;
  prayerTimes: {
    fajr: string;
    shorouq: string;
    duhr: string;
    asr: string;
    maghreb: string;
    eshaa: string;
  };
}) => {
  const getPrayerNameFromArabic = (arabicName: string): PrayerName => {
    const nameMap: Record<string, PrayerName> = {
      الفجر: "fajr",
      الشروق: "shorouq",
      الظهر: "duhr",
      العصر: "asr",
      المغرب: "maghreb",
      العشاء: "eshaa",
    };
    return nameMap[arabicName] || "fajr";
  };

  const status = getPrayerStatus(
    time,
    iqamaShift,
    adhanCountDown,
    getPrayerNameFromArabic(name),
    prayerTimes
  );

  const baseClasses = "grid grid-cols-3 gap-4 items-center p-3 rounded-lg";
  const statusClasses = {
    normal: "bg-gray-100 dark:bg-gray-700", // Changed from "bg-gray-50" to "bg-gray-100"
    approaching: "bg-yellow-50 dark:bg-yellow-900",
    "prayer-time": "bg-green-50 dark:bg-green-900",
    "iqama-time": "bg-blue-50 dark:bg-blue-900",
  };

  const titleClasses = `text-2xl font-bold text-right ${
    status === "approaching"
      ? "text-gray-800 dark:text-white animate-blink" // Blink during approaching
      : status === "normal"
      ? "text-gray-800 dark:text-white"
      : "text-gray-800 dark:text-white"
  }`;

  const timeClasses = `text-4xl font-black digital-clock text-right ${
    status === "approaching"
      ? "text-gray-600 dark:text-gray-300 animate-blink" // Blink during approaching
      : status === "normal"
      ? "text-gray-600 dark:text-gray-300"
      : "text-gray-600 dark:text-gray-300"
  }`;

  const iqamaClasses = `text-4xl font-black digital-clock text-right ${
    status === "prayer-time"
      ? "text-green-600 dark:text-green-300 animate-blink" // Only iqama time blinks during prayer-time
      : status === "iqama-time"
      ? "text-blue-600 dark:text-blue-300"
      : "text-green-600 dark:text-green-400"
  }`;

  return (
    <div className={`${baseClasses} ${statusClasses[status]}`}>
      <div className={titleClasses}>{name}</div>
      <div className={timeClasses}>{time || "--:--"}</div>
      <div className={iqamaClasses}>
        {time ? adjustTime(time, iqamaShift) : "--:--"}
      </div>
    </div>
  );
};
